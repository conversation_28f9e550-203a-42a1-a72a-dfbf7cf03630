# 梦羽AI绘图 API 使用说明

## 概述

梦羽AI绘图提供了RESTful API接口，允许开发者通过编程方式调用图像生成功能。本文档详细介绍了如何使用API进行图像生成。

## 认证

### 获取API密钥

1. 登录您的账户
2. 进入个人中心页面
3. 在"API密钥管理"部分生成您的API密钥
4. 妥善保管您的API密钥，不要在客户端代码中暴露

### 使用API密钥

在所有API请求中，您需要在请求头中包含您的API密钥：

```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

## 接口信息

### 基础信息

- **接口地址**: `POST https://sd.exacg.cc/api/v1/generate_image`
- **请求方式**: POST
- **内容类型**: application/json
- **认证方式**: Bearer Token

### 请求参数

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `prompt` | string | 是 | - | 图像生成提示词 |
| `negative_prompt` | string | 否 | "" | 负面提示词，避免不想要的内容 |
| `width` | integer | 否 | 512 | 图像宽度 (64-2048) |
| `height` | integer | 否 | 512 | 图像高度 (64-2048) |
| `steps` | integer | 否 | 20 | 生成步数 (1-100) |
| `cfg` | float | 否 | 7.0 | CFG引导强度 (1-30) |
| `model_index` | integer | 否 | 0 | 模型索引，参考主页模型列表 |
| `seed` | integer | 否 | -1 | 随机种子，-1为随机 |

### 响应格式

#### 成功响应 (200)

```json
{
  "success": true,
  "message": "图像生成成功",
  "data": {
    "image_url": "https://example.com/image.jpg",
    "image_id": "12345",
    "model_name": "模型名称",
    "points_used": 1,
    "remaining_points": 99
  }
}
```

#### 错误响应 (400/401/500)

```json
{
  "error": "错误描述信息"
}
```

### 常见错误码

- `401`: API密钥无效或缺失
- `400`: 请求参数错误
- `403`: 积分不足
- `500`: 服务器内部错误

## 代码示例

### cURL

```bash
curl -X POST "https://sd.exacg.cc/api/v1/generate_image" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a beautiful anime girl, detailed face, high quality",
    "negative_prompt": "blurry, low quality, distorted",
    "width": 512,
    "height": 512,
    "steps": 20,
    "cfg": 7.0,
    "model_index": 0,
    "seed": -1
  }'
```

### Python

```python
import requests
import json

API_KEY = "YOUR_API_KEY"
BASE_URL = "https://sd.exacg.cc"

def generate_image(prompt, negative_prompt="", width=512, height=512):
    url = f"{BASE_URL}/api/v1/generate_image"
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "prompt": prompt,
        "negative_prompt": negative_prompt,
        "width": width,
        "height": height,
        "steps": 20,
        "cfg": 7.0,
        "model_index": 0,
        "seed": -1
    }
    
    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            print(f"生成成功！图像URL: {result['data']['image_url']}")
            return result['data']['image_url']
        else:
            print(f"生成失败: {result.get('error', '未知错误')}")
    else:
        print(f"请求失败: {response.status_code}")
    
    return None

# 使用示例
image_url = generate_image(
    prompt="a beautiful anime girl, detailed face, high quality",
    negative_prompt="blurry, low quality, distorted"
)
```

### JavaScript (浏览器)

```javascript
const API_KEY = "YOUR_API_KEY";
const BASE_URL = "https://sd.exacg.cc";

async function generateImage(prompt, negativePrompt = "", width = 512, height = 512) {
    const url = `${BASE_URL}/api/v1/generate_image`;
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                prompt: prompt,
                negative_prompt: negativePrompt,
                width: width,
                height: height,
                steps: 20,
                cfg: 7.0,
                model_index: 0,
                seed: -1
            })
        });
        
        const result = await response.json();
        
        if (response.ok && result.success) {
            console.log('生成成功！图像URL:', result.data.image_url);
            return result.data.image_url;
        } else {
            console.error('生成失败:', result.error || '未知错误');
            return null;
        }
    } catch (error) {
        console.error('请求失败:', error);
        return null;
    }
}

// 使用示例
generateImage(
    "a beautiful anime girl, detailed face, high quality",
    "blurry, low quality, distorted"
).then(imageUrl => {
    if (imageUrl) {
        console.log('图像生成完成:', imageUrl);
    }
});
```

### Node.js

```javascript
const axios = require('axios');

const API_KEY = "YOUR_API_KEY";
const BASE_URL = "https://sd.exacg.cc";

async function generateImage(prompt, negativePrompt = "", width = 512, height = 512) {
    const url = `${BASE_URL}/api/v1/generate_image`;
    
    try {
        const response = await axios.post(url, {
            prompt: prompt,
            negative_prompt: negativePrompt,
            width: width,
            height: height,
            steps: 20,
            cfg: 7.0,
            model_index: 0,
            seed: -1
        }, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.data.success) {
            console.log('生成成功！图像URL:', response.data.data.image_url);
            return response.data.data.image_url;
        } else {
            console.error('生成失败:', response.data.error || '未知错误');
            return null;
        }
    } catch (error) {
        console.error('请求失败:', error.response?.data || error.message);
        return null;
    }
}

// 使用示例
(async () => {
    const imageUrl = await generateImage(
        "a beautiful anime girl, detailed face, high quality",
        "blurry, low quality, distorted"
    );
    
    if (imageUrl) {
        console.log('图像生成完成:', imageUrl);
    }
})();
```

## 最佳实践

### 1. 错误处理

始终检查API响应的状态码和返回的错误信息：

```python
try:
    response = requests.post(url, headers=headers, json=data)
    response.raise_for_status()  # 抛出HTTP错误
    result = response.json()
    
    if result.get("success"):
        # 处理成功响应
        pass
    else:
        # 处理业务错误
        print(f"业务错误: {result.get('error')}")
        
except requests.exceptions.RequestException as e:
    # 处理网络错误
    print(f"网络错误: {e}")
except json.JSONDecodeError as e:
    # 处理JSON解析错误
    print(f"JSON解析错误: {e}")
```

### 2. 积分管理

- 每次成功的API调用都会消耗积分
- 生成失败不会扣除积分
- 建议在调用前检查账户余额
- 可以通过响应中的 `remaining_points` 字段监控剩余积分

### 3. 参数优化

- **提示词**: 使用清晰、具体的描述，避免过于复杂的语句
- **负面提示词**: 添加不想要的元素，如 "blurry, low quality, distorted"
- **尺寸**: 根据需要选择合适的图像尺寸，较大尺寸需要更多计算资源
- **步数**: 通常20-30步即可获得良好效果，过多步数可能导致过拟合
- **CFG**: 7-12之间通常效果较好，过高可能导致图像失真

### 4. 安全注意事项

- **API密钥安全**: 不要在客户端代码中暴露API密钥
- **服务器端调用**: 建议在服务器端调用API，避免跨域问题
- **速率限制**: 避免过于频繁的API调用
- **数据验证**: 对用户输入进行适当的验证和过滤

## 常见问题

### Q: 如何获取可用的模型列表？
A: 目前需要参考主页的模型选择器，model_index 从0开始计数。

### Q: 生成的图像会保存多久？
A: 生成的图像会永久保存在云端，您可以随时访问。

### Q: 是否有API调用频率限制？
A: 目前没有严格的频率限制，但建议合理使用，避免过于频繁的调用。

### Q: 如何处理生成失败的情况？
A: 生成失败时不会扣除积分，您可以检查错误信息并重新尝试。

### Q: 可以批量生成图像吗？
A: 目前API不支持批量生成，需要逐个调用。

## 技术支持

如果您在使用API过程中遇到问题，请：

1. 检查API密钥是否正确
2. 确认请求参数格式是否正确
3. 查看错误信息获取具体原因
4. 联系技术支持获取帮助

---

*最后更新时间: 2025-07-26*
