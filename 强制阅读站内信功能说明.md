# 强制阅读站内信功能说明

## 功能概述

为了确保用户不错过重要的站内信息，系统现在实现了强制阅读站内信功能。当用户有未读站内信时，必须先阅读完所有消息才能进入主页。

## 功能特点

### 1. 自动跳转
- 当用户访问主页时，系统会自动检查是否有未读站内信
- 如果有未读消息，会自动跳转到站内信页面（`/messages?force_read=1`）
- 跳转时会在URL中添加 `force_read=1` 参数标识强制阅读模式

### 2. 显眼提示
- 在强制阅读模式下，站内信页面顶部会显示醒目的警告提示
- 提示内容包括：
  - 未读消息数量
  - 强制阅读的说明
  - 操作指引

### 3. 返回限制
- 在强制阅读模式下，"返回主页"按钮会进行检查
- 只有当所有消息都已读后，才允许返回主页
- 如果仍有未读消息，会弹出提示告知用户

### 4. 实时状态更新
- 当用户标记消息为已读时，系统会实时检查是否还有未读消息
- 如果所有消息都已读，会显示成功提示并隐藏警告横幅

## 实现细节

### 后端修改

1. **主页路由修改** (`app.py` 第1016-1030行)
   ```python
   @app.route('/')
   def index():
       # 获取当前用户信息
       current_user = None
       if 'username' in session:
           current_user = user_manager.get_user(session['username'])
           
           # 检查用户是否有未读站内信
           if current_user:
               unread_count = message_system.get_unread_count(session['username'])
               if unread_count > 0:
                   # 如果有未读消息，强制跳转到站内信页面
                   return redirect(url_for('messages_page', force_read=1))

       return render_template('index.html', models=MODELS, current_user=current_user)
   ```

2. **站内信页面路由修改** (`app.py` 第3057-3076行)
   - 添加了 `force_read` 和 `unread_count` 参数传递给模板

3. **新增API端点** (`app.py` 第3188-3214行)
   ```python
   @app.route('/api/messages/check_can_return_home', methods=['GET'])
   @login_required
   def check_can_return_home():
       """检查用户是否可以返回主页（即是否还有未读消息）"""
   ```

### 前端修改

1. **警告横幅** (`templates/messages.html` 第108-130行)
   - 在强制阅读模式下显示醒目的警告提示
   - 包含未读消息数量和操作指引

2. **返回主页按钮** (`templates/messages.html` 第147行)
   - 修改为调用 `checkAndReturnHome()` 函数
   - 在强制阅读模式下会检查权限

3. **JavaScript功能**
   - `checkForceReadStatus()`: 检查强制阅读状态
   - `checkAndReturnHome()`: 检查并返回主页
   - `showSuccess()`: 显示成功提示
   - 在标记已读操作后自动检查状态

## 测试方法

### 1. 创建测试数据
```bash
python test_force_read.py
```

### 2. 测试步骤
1. 使用测试账号登录：
   - 用户名: `testuser`
   - 密码: `123456`

2. 登录后会自动跳转到站内信页面

3. 观察页面顶部的警告提示

4. 尝试点击"返回主页"按钮，会提示需要先阅读消息

5. 点击"全部标记已读"按钮或逐一阅读消息

6. 所有消息已读后，警告提示消失，可以正常返回主页

### 3. 清理测试数据
```bash
python test_force_read.py clear
```

## 用户体验

- **强制性**: 确保重要消息不被忽略
- **友好性**: 提供清晰的提示和快速操作选项
- **实时性**: 状态更新及时，用户体验流畅
- **灵活性**: 支持逐一阅读或批量标记已读

## 注意事项

1. 只有登录用户才会触发强制阅读检查
2. 管理员和普通用户都受此限制
3. 强制阅读只在有未读消息时触发
4. 用户可以通过"全部标记已读"快速完成阅读
5. 系统会实时检查阅读状态，无需刷新页面
