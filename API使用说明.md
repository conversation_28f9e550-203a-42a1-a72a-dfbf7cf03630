# API使用说明

## 概述

本系统提供了RESTful API接口，允许用户通过API密钥调用画图功能。

## 获取API密钥

1. 登录系统后，进入"个人中心"页面
2. 在"API密钥管理"区域点击"生成API密钥"
3. 系统将生成一个唯一的API密钥，格式为：`sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
4. 请妥善保管您的API密钥，不要泄露给他人

## API接口

### 生成图像

**接口地址：** `POST /api/v1/generate_image`

**请求头：**
```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

**请求参数：**

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| prompt | string | 是 | - | 提示词，最大2000字符 |
| negative_prompt | string | 否 | "" | 负面提示词 |
| width | integer | 否 | 512 | 图像宽度 |
| height | integer | 否 | 512 | 图像高度 |
| steps | integer | 否 | 20 | 生成步数 |
| cfg | number | 否 | 7 | CFG值 |
| model_index | integer | 否 | 0 | 模型索引 |
| seed | integer | 否 | -1 | 随机种子，-1为随机 |
| enable_hr | boolean | 否 | false | 启用高分辨率修复 |
| restore_faces | boolean | 否 | false | 面部修复 |

**请求示例：**
```json
{
    "prompt": "a beautiful girl, anime style, high quality",
    "negative_prompt": "low quality, blurry",
    "width": 512,
    "height": 512,
    "steps": 20,
    "cfg": 7,
    "model_index": 0,
    "seed": -1
}
```

**响应格式：**

成功响应：
```json
{
    "success": true,
    "message": "图像生成成功",
    "data": {
        "image_url": "https://example.com/image.jpg",
        "image_id": "12345",
        "model_name": "Miaomiao Harem vPred Dogma 1.1",
        "points_used": 1,
        "remaining_points": 99
    }
}
```

错误响应：
```json
{
    "error": "错误信息"
}
```

## 使用示例

### Python示例

```python
import requests
import json

# API配置
API_KEY = "sk-your-api-key-here"
BASE_URL = "https://sd.exacg.cc"

def generate_image(prompt, negative_prompt="", width=512, height=512):
    url = f"{BASE_URL}/api/v1/generate_image"
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "prompt": prompt,
        "negative_prompt": negative_prompt,
        "width": width,
        "height": height,
        "steps": 20,
        "cfg": 7
    }
    
    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            print(f"生成成功！图像URL: {result['data']['image_url']}")
            return result['data']['image_url']
        else:
            print(f"生成失败: {result.get('message', '未知错误')}")
    else:
        error_data = response.json()
        print(f"请求失败: {error_data.get('error', '未知错误')}")
    
    return None

# 使用示例
if __name__ == "__main__":
    image_url = generate_image(
        prompt="a cute cat, anime style, high quality",
        negative_prompt="low quality, blurry, bad anatomy"
    )
```

### JavaScript示例

```javascript
const API_KEY = "sk-your-api-key-here";
const BASE_URL = "https://sd.exacg.cc";

async function generateImage(prompt, negativePrompt = "", width = 512, height = 512) {
    const url = `${BASE_URL}/api/v1/generate_image`;
    
    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${API_KEY}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            prompt: prompt,
            negative_prompt: negativePrompt,
            width: width,
            height: height,
            steps: 20,
            cfg: 7
        })
    });
    
    const result = await response.json();
    
    if (response.ok && result.success) {
        console.log('生成成功！图像URL:', result.data.image_url);
        return result.data.image_url;
    } else {
        console.error('生成失败:', result.error || result.message);
        return null;
    }
}

// 使用示例
generateImage(
    "a beautiful landscape, oil painting style",
    "low quality, blurry"
).then(imageUrl => {
    if (imageUrl) {
        console.log('图像生成完成:', imageUrl);
    }
});
```

## 注意事项

1. **积分消耗：** 每次API调用将消耗1积分
2. **请求限制：** 请合理使用API，避免频繁请求
3. **密钥安全：** 请妥善保管API密钥，不要在客户端代码中暴露
4. **模型选择：** model_index对应系统中的模型列表，请确保索引有效
5. **错误处理：** 请妥善处理API返回的错误信息

## 错误码说明

| HTTP状态码 | 错误类型 | 说明 |
|------------|----------|------|
| 401 | 认证失败 | API密钥无效或缺失 |
| 400 | 请求错误 | 参数格式错误或缺少必需参数 |
| 500 | 服务器错误 | 服务器内部错误 |

## 支持

如有问题，请联系系统管理员或在系统内提交反馈。
